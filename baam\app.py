from flask import Flask, request, jsonify, make_response, after_this_request
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from datetime import datetime
import uuid
import os
import time
import logging
from dotenv import load_dotenv
from firebase_service import FirebaseService
from openai_service import OpenAIService
from models import User, Goal, Task, Conversation, AIInsight
from pydantic import ValidationError
from security import require_auth, require_security_headers, get_combined_identifier

def serialize_context(data):
    """Recursively serialize context data, converting datetimes to ISO strings."""
    if isinstance(data, dict):
        return {k: serialize_context(v) for k, v in data.items()}
    if isinstance(data, list):
        return [serialize_context(i) for i in data]
    if isinstance(data, datetime):
        return data.isoformat()
    return data

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/')
def health_check():
    return jsonify({'status': 'ok'}), 200

# Security configuration
allowed_origins = [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:8081',
    'http://*************:8081',  # Expo dev server on local network
    'https://yourdomain.com',  # Add your production domain
]

# For development, we'll also allow any local network IP
import re
def is_local_network_origin(origin):
    """Check if origin is from local network (192.168.x.x, 10.x.x.x, 172.16-31.x.x)"""
    if not origin:
        return False

    # Extract hostname from origin
    match = re.match(r'https?://([^:]+)', origin)
    if not match:
        return False

    hostname = match.group(1)

    # Check for local network patterns
    local_patterns = [
        r'^192\.168\.\d+\.\d+$',
        r'^10\.\d+\.\d+\.\d+$',
        r'^172\.(1[6-9]|2[0-9]|3[0-1])\.\d+\.\d+$',
        r'^localhost$',
        r'^127\.0\.0\.1$'
    ]

    return any(re.match(pattern, hostname) for pattern in local_patterns)

# Build dynamic origins list for CORS
def get_cors_origins():
    """Get list of allowed origins for CORS"""
    origins = allowed_origins.copy()

    # In development, add common local network patterns
    if os.getenv('FLASK_ENV') == 'development':
        # Add common local network ranges
        local_origins = [
            'http://localhost:*',
            'http://127.0.0.1:*',
            'http://192.168.*.*:*',
            'http://10.*.*.*:*',
            'http://172.16.*.*:*',
            'http://172.17.*.*:*',
            'http://172.18.*.*:*',
            'http://172.19.*.*:*',
            'http://172.20.*.*:*',
            'http://172.21.*.*:*',
            'http://172.22.*.*:*',
            'http://172.23.*.*:*',
            'http://172.24.*.*:*',
            'http://172.25.*.*:*',
            'http://172.26.*.*:*',
            'http://172.27.*.*:*',
            'http://172.28.*.*:*',
            'http://172.29.*.*:*',
            'http://172.30.*.*:*',
            'http://172.31.*.*:*'
        ]
        origins.extend(local_origins)

    return origins

# Configure CORS with security headers
CORS(app,
     resources={
         r"/*": {
             "origins": get_cors_origins(),
             "methods": ["GET", "POST", "OPTIONS", "PUT", "DELETE"],
             "allow_headers": [
                 "Authorization",
                 "Content-Type",
                 "X-Content-Type-Options",
                 "X-Frame-Options",
                 "X-Request-Timestamp",
                 "X-Request-Nonce",
                 "Content-Security-Policy"
             ],
             "expose_headers": [
                 "Authorization",
                 "Content-Type",
                 "X-Content-Type-Options",
                 "X-Frame-Options",
                 "Strict-Transport-Security",
                 "X-Response-Timestamp",
                 "Content-Security-Policy"
             ],
             "supports_credentials": True,
             "max_age": 3600
         }
     })

# Rate limiting configuration
DEFAULT_LIMITS = ["200 per day", "50 per hour", "10 per minute"]

limiter = Limiter(
    app=app,
    key_func=get_combined_identifier,
    default_limits=DEFAULT_LIMITS,
    storage_uri="memory://"
)

# Initialize services
firebase_service = FirebaseService()
openai_service = OpenAIService()

@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    # Add timestamp for response validation
    response.headers['X-Response-Timestamp'] = str(int(time.time() * 1000))

    # Security headers
    response.headers.update({
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://identitytoolkit.googleapis.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; frame-ancestors 'none';"
    })

    # Ensure CORS exposes these headers
    response.headers[
        'Access-Control-Expose-Headers'] = 'Content-Security-Policy, Strict-Transport-Security, X-Content-Type-Options, X-Frame-Options, X-Response-Timestamp'

    return response

@app.errorhandler(429)
def ratelimit_handler(e):
    """Handle rate limit exceeded errors"""
    logger.warning(f"Rate limit exceeded for {request.remote_addr}: {str(e.description)}")
    return jsonify({
        "error": "Rate limit exceeded",
        "message": str(e.description)
    }), 429

@app.errorhandler(Exception)
def handle_error(error):
    """Global error handler with security headers"""
    logger.error(f"Unhandled error: {str(error)}", exc_info=True)
    
    response = jsonify({
        'error': str(error),
        'status': getattr(error, 'code', 500)
    })

    # Add security headers even to error responses
    response.headers[
        'Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-Response-Timestamp'] = str(int(time.time() * 1000))

    return response, getattr(error, 'code', 500)

# Authentication endpoint
@app.route('/auth', methods=['POST'])
@limiter.limit("100 per minute, 100 per hour")
def authenticate():
    try:
        data = request.get_json()
        auth_header = request.headers.get('Authorization')
        
        # Support both header and body for backward compatibility
        id_token = None
        if auth_header and auth_header.startswith('Bearer '):
            id_token = auth_header.split('Bearer ')[1]
        elif data and 'Authorization' in data:
            id_token = data['Authorization']
        elif data and 'idToken' in data:
            id_token = data['idToken']
            
        if not id_token:
            return jsonify({'error': 'No ID token provided'}), 400

        # Verify the Firebase ID token
        from firebase_admin import auth
        decoded_token = auth.verify_id_token(id_token)

        return jsonify({
            'uid': decoded_token['uid'],
            'expires_in': decoded_token['exp'] - time.time()
        })
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Handle Availability Route
@app.route('/api/handles/<handle>', methods=['GET'])
@limiter.limit("60 per minute")
def check_handle_availability(handle):
    """Check if a user handle is already taken."""
    is_taken = firebase_service.is_handle_taken(handle)
    return jsonify({'is_taken': is_taken})

# User API Routes

@app.route('/api/users', methods=['POST'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def create_user():
    """Create a new user document after Firebase authentication."""
    try:
        data = request.get_json()
        auth_user = request.user  # Injected by @require_auth

        # Basic validation
        if not all(k in data for k in ['first_name', 'last_name', 'handle']):
            return jsonify({'error': 'Missing required fields: first_name, last_name, handle'}), 400

        user_data = {
            'uid': auth_user['uid'],
            'email': auth_user.get('email', ''),
            'first_name': data['first_name'],
            'last_name': data['last_name'],
            'handle': data['handle'],
        }

        user = User(**user_data)
        user_id = firebase_service.create_user(user.to_dict())
        return jsonify({'uid': user_id, 'message': 'User created successfully'}), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid user data', 'details': e.errors()}), 400
    except ValueError as e:
        # Catches handle-taken errors from firebase_service
        return jsonify({'error': str(e)}), 409
    except Exception as e:
        logger.error(f"User creation failed: {str(e)}", exc_info=True)
        return jsonify({'error': 'An unexpected error occurred during user creation.'}), 500

@app.route('/api/users/<uid>', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_user(uid):
    """Get user by UID"""
    user = firebase_service.get_user(uid)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    return jsonify(user)

@app.route('/api/users/<uid>', methods=['PUT'])
@limiter.limit("20 per minute")
@require_auth
@require_security_headers
def update_user(uid):
    """Update user data"""
    try:
        data = request.get_json()
        firebase_service.update_user(uid, data)
        return jsonify({'message': 'User updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Goal API Routes

@app.route('/api/goals', methods=['POST'])
@limiter.limit("20 per minute")
@require_auth
@require_security_headers
def create_goal():
    """Create a new goal"""
    try:
        data = request.get_json()
        goal = Goal(**data)
        goal_id = firebase_service.create_goal(goal.to_dict())
        return jsonify({'goal_id': goal_id, 'message': 'Goal created successfully'}), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid goal data', 'details': e.errors()}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<uid>/goals', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_user_goals(uid):
    """Get all goals for a user"""
    goals = firebase_service.get_goals_by_user(uid)
    return jsonify(goals)

@app.route('/api/goals/<goal_id>', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_goal(goal_id):
    """Get goal by ID"""
    goal = firebase_service.get_goal(goal_id)
    if not goal:
        return jsonify({'error': 'Goal not found'}), 404
    return jsonify(goal)

@app.route('/api/goals/<goal_id>', methods=['PUT'])
@limiter.limit("20 per minute")
@require_auth
@require_security_headers
def update_goal(goal_id):
    """Update goal"""
    try:
        data = request.get_json()
        firebase_service.update_goal(goal_id, data)
        return jsonify({'message': 'Goal updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/goals/<goal_id>', methods=['DELETE'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def delete_goal(goal_id):
    """Delete goal"""
    try:
        firebase_service.delete_goal(goal_id)
        return jsonify({'message': 'Goal deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Task API Routes

@app.route('/api/tasks', methods=['POST'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def create_task():
    """Create a new task"""
    try:
        data = request.get_json()
        
        # Auto-estimate duration if not provided
        if 'estimated_duration' not in data or not data['estimated_duration']:
            duration = openai_service.estimate_task_duration(
                data.get('task_name', ''),
                data.get('notes', ''),
                data.get('categories', ['general'])[0] if data.get('categories') else 'general'
            )
            data['estimated_duration'] = duration
        
        task = Task(**data)
        task_id = firebase_service.create_task(task.to_dict())
        return jsonify({'task_id': task_id, 'message': 'Task created successfully'}), 201
    except ValidationError as e:
        return jsonify({'error': 'Invalid task data', 'details': e.errors()}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<uid>/tasks', methods=['GET'])
@limiter.limit("50 per minute")
@require_auth
@require_security_headers
def get_user_tasks(uid):
    """Get all tasks for a user"""
    tasks = firebase_service.get_tasks_by_user(uid)
    return jsonify(tasks)

@app.route('/api/tasks/<task_id>', methods=['GET'])
@limiter.limit("50 per minute")
@require_auth
@require_security_headers
def get_task(task_id):
    """Get task by ID"""
    task = firebase_service.get_task(task_id)
    if not task:
        return jsonify({'error': 'Task not found'}), 404
    return jsonify(task)

@app.route('/api/tasks/<task_id>', methods=['PUT'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def update_task(task_id):
    """Update task"""
    try:
        data = request.get_json()
        now_iso = datetime.utcnow().isoformat()
        status = data.get('status')
        if status is not None:
            # Normalize and set completed_at accordingly
            if status == 'completed':
                data['completed_at'] = data.get('completed_at') or now_iso
            else:
                # Clearing completed_at when moving away from completed
                data['completed_at'] = None
        # Always bump updated_at
        data['updated_at'] = now_iso
        firebase_service.update_task(task_id, data)
        return jsonify({'message': 'Task updated successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tasks/<task_id>', methods=['DELETE'])
@limiter.limit("20 per minute")
@require_auth
@require_security_headers
def delete_task(task_id):
    """Delete task"""
    try:
        firebase_service.delete_task(task_id)
        return jsonify({'message': 'Task deleted successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/goals/<goal_id>/tasks', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_goal_tasks(goal_id):
    """Get all tasks for a specific goal"""
    tasks = firebase_service.get_tasks_by_goal(goal_id)
    return jsonify(tasks)

# AI-Powered Scheduling

@app.route('/api/tasks/<task_id>/schedule', methods=['POST'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def schedule_task_ai(task_id):
    """AI-powered task scheduling - find optimal time for a task"""
    try:
        from scheduler import schedule_task
        
        data = request.get_json()
        preferred_time = data.get('preferred_time')  # Optional
        
        # Get the task to schedule
        task = firebase_service.get_task(task_id)
        if not task:
            return jsonify({'error': 'Task not found'}), 404
        
        # Get user's existing scheduled tasks
        existing_tasks = firebase_service.get_tasks_by_user(task['uid'])
        existing_tasks = [t for t in existing_tasks if t.get('scheduled_time') and t['task_id'] != task_id]
        
        # Convert to Task objects
        from models import Task, TaskStatus
        task_obj = Task(
            task_id=task['task_id'],
            uid=task['uid'],
            title=task['title'],
            description=task.get('description', ''),
            status=TaskStatus(task.get('status', 'planned')),
            estimated_duration=task.get('estimated_duration', 60),
            flexibility=task.get('flexibility', 5),
            earliest_start=datetime.fromisoformat(task['earliest_start']) if task.get('earliest_start') else None,
            latest_finish=datetime.fromisoformat(task['latest_finish']) if task.get('latest_finish') else None
        )
        
        existing_task_objs = []
        for t in existing_tasks:
            if t.get('scheduled_time'):
                existing_task_objs.append(Task(
                    task_id=t['task_id'],
                    uid=t['uid'],
                    title=t['title'],
                    scheduled_time=datetime.fromisoformat(t['scheduled_time']),
                    estimated_duration=t.get('estimated_duration', 60),
                    flexibility=t.get('flexibility', 5)
                ))
        
        # Parse preferred time if provided
        preferred_dt = None
        if preferred_time:
            preferred_dt = datetime.fromisoformat(preferred_time)
        
        # Run scheduling algorithm
        result = schedule_task(task_obj, existing_task_objs, preferred_dt)
        
        if result['success']:
            # Update task with scheduled time
            firebase_service.update_task(task_id, {
                'scheduled_time': result['scheduled_time'].isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            })
            
            # Update any moved tasks
            for moved in result.get('moved_tasks', []):
                firebase_service.update_task(moved['task_id'], {
                    'scheduled_time': moved['new_time'].isoformat(),
                    'updated_at': datetime.utcnow().isoformat()
                })
        
        return jsonify(result), 200 if result['success'] else 409
        
    except Exception as e:
        app.logger.error(f"Task scheduling error: {str(e)}")
        return jsonify({'error': 'Failed to schedule task'}), 500

@app.route('/api/users/<uid>/schedule/daily', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_daily_schedule(uid):
    """Get user's schedule for a specific day"""
    try:
        # Get date from query params (default to today)
        date_str = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # Get all user tasks
        all_tasks = firebase_service.get_tasks_by_user(uid)
        
        # Filter tasks for the target date
        daily_tasks = []
        for task in all_tasks:
            if task.get('scheduled_time'):
                task_date = datetime.fromisoformat(task['scheduled_time']).date()
                if task_date == target_date:
                    daily_tasks.append(task)
        
        # Sort by scheduled time
        daily_tasks.sort(key=lambda x: x['scheduled_time'])
        
        return jsonify({
            'date': date_str,
            'tasks': daily_tasks,
            'total_tasks': len(daily_tasks),
            'total_duration': sum(t.get('estimated_duration', 0) for t in daily_tasks)
        }), 200
        
    except Exception as e:
        app.logger.error(f"Daily schedule error: {str(e)}")
        return jsonify({'error': 'Failed to get daily schedule'}), 500

@app.route('/api/users/<uid>/tasks/timeframe', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_tasks_in_timeframe(uid):
    """Get user's tasks within a specific time frame"""
    try:
        # Get time range from query params
        start_time = request.args.get('start_time')  # ISO format: 2024-01-01T09:00:00
        end_time = request.args.get('end_time')     # ISO format: 2024-01-07T18:00:00
        
        if not start_time or not end_time:
            return jsonify({'error': 'start_time and end_time parameters are required'}), 400
        
        try:
            start_dt = datetime.fromisoformat(start_time)
            end_dt = datetime.fromisoformat(end_time)
        except ValueError:
            return jsonify({'error': 'Invalid datetime format. Use ISO format: YYYY-MM-DDTHH:MM:SS'}), 400
        
        # Get all user tasks
        all_tasks = firebase_service.get_tasks_by_user(uid)
        
        # Filter tasks within the time frame
        timeframe_tasks = []
        for task in all_tasks:
            if task.get('scheduled_time'):
                task_dt = datetime.fromisoformat(task['scheduled_time'])
                if start_dt <= task_dt <= end_dt:
                    timeframe_tasks.append(task)
            elif task.get('due_date'):
                # Include tasks with due dates in the range (even if not scheduled)
                due_dt = datetime.fromisoformat(task['due_date'])
                if start_dt <= due_dt <= end_dt:
                    timeframe_tasks.append(task)
        
        # Sort by scheduled time, then by due date
        timeframe_tasks.sort(key=lambda x: (
            x.get('scheduled_time') or x.get('due_date') or '9999-12-31T23:59:59'
        ))
        
        return jsonify({
            'start_time': start_time,
            'end_time': end_time,
            'tasks': timeframe_tasks,
            'total_tasks': len(timeframe_tasks),
            'scheduled_tasks': len([t for t in timeframe_tasks if t.get('scheduled_time')]),
            'unscheduled_tasks': len([t for t in timeframe_tasks if not t.get('scheduled_time')]),
            'total_duration': sum(t.get('estimated_duration', 0) for t in timeframe_tasks)
        }), 200
        
    except Exception as e:
        app.logger.error(f"Timeframe tasks error: {str(e)}")
        return jsonify({'error': 'Failed to get tasks in timeframe'}), 500

@app.route('/api/users/<uid>/tasks/suggestions', methods=['POST'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def get_task_suggestions(uid):
    """Get AI-generated task suggestions based on user goals"""
    try:
        if not openai_service:
            return jsonify({'error': 'AI features are currently disabled. Please configure OpenAI API key.'}), 503
            
        goals = firebase_service.get_goals_by_user(uid)
        suggestions = openai_service.generate_task_suggestions(goals)
        return jsonify({'suggestions': suggestions})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<uid>/insights', methods=['GET'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def get_productivity_insights(uid):
    """Get productivity insights for user"""
    try:
        # Get completed tasks for analysis
        all_tasks = firebase_service.get_tasks_by_user(uid)
        completed_tasks = [t for t in all_tasks if t.get('status') == 'completed']
        
        if completed_tasks:
            insights = openai_service.analyze_productivity_patterns(completed_tasks)
            
            # Save insights to database
            for category, data in insights.get('category_insights', {}).items():
                insight_data = {
                    'uid': uid,
                    'task_category': category,
                    'average_duration': data.get('average_duration', 60),
                    'optimal_time_slots': insights.get('optimal_time_slots', []),
                    'completion_rate': data.get('completion_rate', 0.8)
                }
                firebase_service.create_ai_insight(insight_data)
            
            return jsonify(insights)
        else:
            return jsonify({
                'message': 'Not enough completed tasks for analysis',
                'optimal_time_slots': ['09:00-12:00', '14:00-17:00'],
                'productivity_score': 0.75,
                'recommendations': ['Complete more tasks to get personalized insights']
            })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
@limiter.limit("10 per minute")
@require_auth
@require_security_headers
def chat_with_ai():
    """Chat with AI assistant about scheduling"""
    try:
        from chat_actions import ChatActionBuilder, create_response_with_actions, create_simple_response
        from scheduler import schedule_task
        from models import Task, TaskStatus
        
        data = request.get_json()
        user_message = data.get('message', '')
        uid = request.user.get('uid')
        
        # Get user context
        user_tasks = firebase_service.get_tasks_by_user(uid)
        user_goals = firebase_service.get_goals_by_user(uid)
        
        # Get conversation history (last 10 messages)
        conversations = firebase_service.get_conversations_by_user(uid, limit=10)
        
        # Serialize context to ensure JSON compatibility
        user_context = {
            'tasks': serialize_context(user_tasks),
            'goals': serialize_context(user_goals)
        }
        serialized_conversations = serialize_context(conversations)

        # Generate chat response from OpenAI
        ai_response = openai_service.generate_chat_response(
            user_message,
            serialized_conversations,
            user_context
        )

        if not ai_response.get('success'):
            return jsonify({'error': ai_response.get('response', 'AI service failed')}), 500

        # Structure the final response
        chat_response = {
            'message': ai_response.get('response'),
            'actions': [],  # Placeholder for future structured actions
            'context': user_context
        }

        # Store conversation in Firebase
        conversation_data = {
            'uid': uid,
            'message': user_message,
            'response': chat_response['message'],
            'timestamp': datetime.utcnow(),
            'session_id': data.get('session_id', 'default'),
            'message_type': 'user_query',
            'actions_proposed': len(chat_response.get('actions', []))
        }
        conversation_id = firebase_service.create_conversation(conversation_data)
        chat_response['conversation_id'] = conversation_id
        
        return jsonify(chat_response), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Conversation API Routes

@app.route('/api/users/<uid>/conversations', methods=['GET'])
@limiter.limit("30 per minute")
@require_auth
@require_security_headers
def get_user_conversations(uid):
    """Get conversation history for user"""
    limit = request.args.get('limit', 20, type=int)
    conversations = firebase_service.get_conversations_by_user(uid, limit)
    return jsonify(conversations)

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
