import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import Firebase config from JSON file
import firebaseConfig from '../config/firebase-config.json';

// Polyfill localStorage for React Native
if (Platform.OS !== 'web' && typeof window !== 'undefined' && !window.localStorage) {
  window.localStorage = {
    getItem: async (key: string) => {
      try {
        return await AsyncStorage.getItem(key);
      } catch (error) {
        console.error('AsyncStorage getItem error:', error);
        return null;
      }
    },
    setItem: async (key: string, value: string) => {
      try {
        await AsyncStorage.setItem(key, value);
      } catch (error) {
        console.error('AsyncStorage setItem error:', error);
      }
    },
    removeItem: async (key: string) => {
      try {
        await AsyncStorage.removeItem(key);
      } catch (error) {
        console.error('AsyncStorage removeItem error:', error);
      }
    },
    clear: async () => {
      try {
        await AsyncStorage.clear();
      } catch (error) {
        console.error('AsyncStorage clear error:', error);
      }
    },
    key: (_index: number) => {
      // This is a synchronous method that AsyncStorage doesn't support
      // Return null as fallback
      return null;
    },
    get length() {
      // This is a synchronous property that AsyncStorage doesn't support
      // Return 0 as fallback
      return 0;
    }
  } as any;
}

// Initialize Firebase
if (!firebase.apps.length) {
  firebase.initializeApp(firebaseConfig);
} else {
  firebase.app(); // if already initialized, use that one
}

const auth = firebase.auth();

// Set persistence based on platform
if (Platform.OS === 'web' && typeof window !== 'undefined' && window.localStorage) {
  // Use LOCAL persistence for web browsers
  auth.setPersistence(firebase.auth.Auth.Persistence.LOCAL);
} else if (Platform.OS !== 'web') {
  // For React Native, use LOCAL persistence with our AsyncStorage polyfill
  auth.setPersistence(firebase.auth.Auth.Persistence.LOCAL);
}

// Authentication functions
export const signIn = async (email: string, password: string) => {
  return await auth.signInWithEmailAndPassword(email, password);
};

export const signUp = async (email: string, password: string) => {
  return await auth.createUserWithEmailAndPassword(email, password);
};

export const logOut = async (): Promise<void> => {
  return await auth.signOut();
};

export { auth };

export const listenToAuthChanges = (callback: (user: firebase.User | null) => void) => {
  return auth.onAuthStateChanged(callback);
};
